import { MongoClient } from 'mongodb';

if (!process.env.MONGODB_URI) {
  throw new Error('Please add your MongoDB URI to .env');
}

const uri = process.env.MONGODB_URI;
const options = {};

let client: MongoClient;
let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options);
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

export { clientPromise };

// Database helper functions
export async function getDatabase() {
  const client = await clientPromise;
  return client.db('flyfiles');
}

export async function getUsersCollection() {
  const db = await getDatabase();
  return db.collection('users');
}

export async function getFilesCollection() {
  const db = await getDatabase();
  return db.collection('files');
}

export async function getSessionsCollection() {
  const db = await getDatabase();
  return db.collection('sessions');
}

export async function getDownloadLogsCollection() {
  const db = await getDatabase();
  return db.collection('downloadLogs');
}

// Plan configurations
export const PLAN_CONFIGS = {
  guest: {
    name: 'Guest',
    uploadLimit: {
      amount: 250 * 1024 * 1024, // 250MB
      period: 'session' as const
    },
    fileExpiry: 7, // days
    downloadLimits: {
      configurable: false,
      unlimited: true
    },
    features: [
      '250MB per session',
      '7-day file expiry',
      'Unlimited downloads',
      'No account required'
    ]
  },
  free: {
    name: 'Free Account',
    uploadLimit: {
      amount: 15 * 1024 * 1024 * 1024, // 15GB
      period: 'month' as const
    },
    fileExpiry: 10, // days
    downloadLimits: {
      configurable: true,
      unlimited: false
    },
    features: [
      '15GB monthly upload',
      '10-day file expiry',
      'Configurable download limits',
      'Google login required'
    ]
  },
  upgrade1: {
    name: 'Upgrade 1',
    uploadLimit: {
      amount: 15 * 1024 * 1024 * 1024, // 15GB
      period: 'week' as const
    },
    fileExpiry: 14, // days
    downloadLimits: {
      configurable: true,
      unlimited: false
    },
    features: [
      '15GB weekly upload',
      '14-day file expiry',
      'Download statistics',
      'All Free features'
    ],
    price: {
      amount: 5,
      period: 'month' as const
    }
  },
  upgrade2: {
    name: 'Upgrade 2',
    uploadLimit: {
      amount: 50 * 1024 * 1024 * 1024, // 50GB
      period: 'week' as const
    },
    fileExpiry: 30, // days
    downloadLimits: {
      configurable: true,
      unlimited: true
    },
    features: [
      '50GB weekly upload',
      '30-day file expiry',
      'Unlimited downloads',
      'Advanced analytics',
      'All previous features'
    ],
    price: {
      amount: 25,
      period: 'month' as const
    }
  }
} as const; 