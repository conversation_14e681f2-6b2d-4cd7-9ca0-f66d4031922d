"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Check, Star } from "lucide-react"
import { PLAN_CONFIGS } from "@/app/lib/mongodb"
import { formatFileSize } from "@/app/lib/utils"

const plans = [
  {
    id: 'guest',
    config: PLAN_CONFIGS.guest,
    popular: false,
    action: 'Start som gæst',
    href: '/'
  },
  {
    id: 'free',
    config: PLAN_CONFIGS.free,
    popular: true,
    action: 'Log ind gratis',
    href: '/login'
  },
  {
    id: 'upgrade1',
    config: PLAN_CONFIGS.upgrade1,
    popular: false,
    action: 'Vælg Upgrade 1',
    href: '/login'
  },
  {
    id: 'upgrade2',
    config: PLAN_CONFIGS.upgrade2,
    popular: false,
    action: 'Vælg Upgrade 2',
    href: '/login'
  }
]

export default function PricingPage() {
  return (
    <div className="min-h-screen py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Vælg din plan
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Fra gratis gæsteadgang til professionelle løsninger. Alle planer inkluderer sikker kryptering og automatisk fil-sletning.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {plans.map((plan) => (
            <Card key={plan.id} className={`relative ${plan.popular ? 'border-blue-500 shadow-lg scale-105' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Star className="h-3 w-3" />
                    <span>Populær</span>
                  </div>
                </div>
              )}
              
              <CardHeader>
                <CardTitle className="text-center">{plan.config.name}</CardTitle>
                <CardDescription className="text-center">
                  {'price' in plan.config ? (
                    <div>
                      <span className="text-3xl font-bold text-gray-900 dark:text-white">
                        {plan.config.price.amount} kr
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">/{plan.config.price.period}</span>
                    </div>
                  ) : (
                    <span className="text-3xl font-bold text-gray-900 dark:text-white">Gratis</span>
                  )}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="text-center mb-4">
                    <div className="text-lg font-semibold">
                      {formatFileSize(plan.config.uploadLimit.amount)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      per {plan.config.uploadLimit.period === 'session' ? 'session' : 
                           plan.config.uploadLimit.period === 'week' ? 'uge' : 'måned'}
                    </div>
                  </div>

                  <ul className="space-y-2">
                    {plan.config.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <a href={plan.href}>
                  <Button 
                    className="w-full"
                    variant={plan.popular ? "default" : "outline"}
                  >
                    {plan.action}
                  </Button>
                </a>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Ofte stillede spørgsmål
          </h2>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Hvordan fungerer gæsteadgang?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400">
                  Som gæst kan du uploade op til 250MB per browsersession uden at oprette en konto. 
                  Dine filer bliver automatisk slettet efter 7 dage.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Hvad sker der med mine filer efter udløb?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400">
                  Alle filer bliver automatisk og permanent slettet fra vores servere når de udløber. 
                  Dette sikrer din privatliv og overholder GDPR-reglerne.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Kan jeg opgradere eller nedgradere min plan?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400">
                  Ja, du kan til enhver tid ændre din plan. Ved opgraderinger får du adgang til de nye funktioner med det samme. 
                  Ved nedgraderinger træder ændringerne i kraft ved næste faktureringsperiode.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Er mine filer sikre?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400">
                  Ja, alle filer krypteres under upload og opbevaring. Vi bruger industri-standard sikkerhed 
                  og overholder danske GDPR-regler for databeskyttelse.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Klar til at komme i gang?
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Start med vores gratis plan eller prøv som gæst
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/login">
              <Button size="lg">Opret gratis konto</Button>
            </a>
            <a href="/">
              <Button size="lg" variant="outline">Prøv som gæst</Button>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
} 